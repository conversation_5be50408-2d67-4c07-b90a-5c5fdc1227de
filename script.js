function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

class VCREffect {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.ctx = canvas.getContext("2d");
        this.config = Object.assign({
            fps: 60,
            blur: 1,
            opacity: 1,
            miny: 220,
            miny2: 220,
            num: 70
        }, options);

        this.init();
    }

    init() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        this.canvas.style.position = "absolute";
        this.canvas.style.top = "0";
        this.canvas.style.left = "0";
        this.canvas.style.opacity = this.config.opacity;

        this.generateVCRNoise();
        window.addEventListener("resize", () => this.onResize());
    }

    onResize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    generateVCRNoise() {
        if (this.config.fps >= 60) {
            cancelAnimationFrame(this.vcrInterval);
            const animate = () => {
                this.renderTrackingNoise();
                this.vcrInterval = requestAnimationFrame(animate);
            };
            animate();
        } else {
            clearInterval(this.vcrInterval);
            this.vcrInterval = setInterval(() => {
                this.renderTrackingNoise();
            }, 1000 / this.config.fps);
        }
    }

    renderTrackingNoise(radius = 2) {
        const { canvas, ctx, config } = this;
        let { miny, miny2, num } = config;

        canvas.style.filter = `blur(${config.blur}px)`;
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = `#fff`;

        ctx.beginPath();
        for (let i = 0; i <= num; i++) {
            let x = Math.random() * canvas.width;
            let y1 = getRandomInt(miny += 3, canvas.height);
            let y2 = getRandomInt(0, miny2 -= 3);
            ctx.fillRect(x, y1, radius, radius);
            ctx.fillRect(x, y2, radius, radius);
            ctx.fill();

            this.renderTail(ctx, x, y1, radius);
            this.renderTail(ctx, x, y2, radius);
        }
        ctx.closePath();
    }

    renderTail(ctx, x, y, radius) {
        const n = getRandomInt(1, 50);
        const dirs = [1, -1];
        let dir = dirs[Math.floor(Math.random() * dirs.length)];

        for (let i = 0; i < n; i++) {
            let r = getRandomInt(radius - 0.01, radius);
            let dx = getRandomInt(1, 4) * dir;
            radius -= 0.1;
            ctx.fillRect((x += dx), y, r, r);
            ctx.fill();
        }
    }
}

// Usage
const canvas = document.getElementById("canvas");
const vcrEffect = new VCREffect(canvas, {
    opacity: 1,
    miny: 220,
    miny2: 220,
    num: 70,
    fps: 60,
    blur: 1
});
// Wolf CTF Registration Confirmation Message
const ctfMessage = `Thank You for Registering!
We're thrilled to have you 
on 
board for the ultimate cybersecurity showdown.

Cyber Wolf Presents: WOLF CTF
Date: July 23 – 24, 2025
Time: 10:00 AM to 10:00 AM (IST)
Mode: Online
organized by: Cyber Wolf Team
Website:https://cyber-wolf-free-training.web.app/

Challenge Categories:
• Web Exploitation
• Network Exploits
• Penetration Testing

Get ready to test your skills, solve real-world
scenarios, and showcase your expertise in the world of cybersecurity.
Participate in thrilling challenges, learn from industry experts,
and compete with fellow enthusiasts from around the globe.

Key Highlights:
• 24-Hour Online Event
• Prizes Worth Cerificate Top 3
• Expert Workshops
• Networking Opportunities
• Global Participation

Join us for an exhilarating experience filled with learning,
challenging scenarios, and rise through the leaderboard.
Stay tuned — timing details and platform access will be shared soon.

@Cyber Wolf:- Wolf CTF`;

// Create scrolling text effect
function createScrollingText() {
  const container = document.querySelector('.tv-container');
  const textElement = document.createElement('div');
  textElement.className = 'ctf-message';
  textElement.innerHTML = ctfMessage.replace(/\n/g, '<br>');
  container.appendChild(textElement);

  // Start animation
  setTimeout(() => {
    textElement.classList.add('animate');
  }, 500);
}

// Handle video and text initialization
function initializeCTFDisplay() {
  // Initialize power button first
  initializePowerButton();

  // Don't start other components until TV is turned on
}

// Start all TV functionality when powered on
function startTVFunctionality() {
  const video = document.getElementById('ctfvideo');

  // Create scrolling text
  createScrollingText();

  // Initialize CTF news icon
  initializeCTFNewsIcon();

  // Handle video events
  if (video) {
    // Set initial volume and ensure audio is enabled
    video.volume = 0.8; // 80% volume
    video.muted = false; // Ensure audio is not muted

    video.addEventListener('loadeddata', () => {
      console.log('CTF video loaded successfully');
      // Force play when data is loaded
      playVideoWithSound(video);
    });

    video.addEventListener('canplay', () => {
      // Try to play as soon as video can play
      playVideoWithSound(video);
    });

    video.addEventListener('error', () => {
      console.log('Video failed to load, continuing with text only');
      // Hide video if it fails to load
      video.style.display = 'none';
    });

    // Initial play attempt
    playVideoWithSound(video);

    // Add click listener to document to enable audio on user interaction
    document.addEventListener('click', () => {
      playVideoWithSound(video);
    }, { once: true });
  }
}

// Function to play video with sound
function playVideoWithSound(video) {
  if (video.paused) {
    video.muted = false;
    video.volume = 0.8;

    const playPromise = video.play();

    if (playPromise !== undefined) {
      playPromise.then(() => {
        console.log('Video playing with sound at', Math.round(video.volume * 100) + '% volume');
      }).catch((error) => {
        console.log('Autoplay blocked:', error.message);
        // Try with muted first, then unmute
        video.muted = true;
        video.play().then(() => {
          console.log('Video playing muted, will unmute on user interaction');
          // Unmute after a short delay
          setTimeout(() => {
            video.muted = false;
            console.log('Video unmuted');
          }, 1000);
        }).catch(() => {
          console.log('Video play failed completely');
        });
      });
    }
  }
}

// CTF News Icon functionality
function initializeCTFNewsIcon() {
  const newsIcon = document.getElementById('ctfNewsIcon');

  if (newsIcon) {
    newsIcon.addEventListener('click', handleCTFNewsClick);
    newsIcon.addEventListener('touchstart', handleCTFNewsClick);
  }
}

function handleCTFNewsClick() {
  const newsIcon = document.getElementById('ctfNewsIcon');

  // Add click effect
  newsIcon.style.transform = 'scale(0.9)';
  setTimeout(() => {
    newsIcon.style.transform = '';
  }, 150);

  // Show CTF news popup
  showCTFNewsPopup();

  // Also ensure video plays with sound on touch
  const video = document.getElementById('ctfvideo');
  if (video) {
    playVideoWithSound(video);
  }
}

function showCTFNewsPopup() {
  // Create news popup
  const popup = document.createElement('div');
  popup.className = 'ctf-news-popup';
  popup.innerHTML = `
    <div class="news-content">
      <div class="news-header">
        <h2>🚨 CTF NEWS ALERT 🚨</h2>
        <button class="close-btn" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
      </div>
      <div class="news-body">
        <p><strong>🔥 BREAKING:</strong> Wolf CTF Registration is LIVE!</p>
        <p><strong>📅 Event Date:</strong> July 23-24, 2025</p>
        <p><strong>⏰ Duration:</strong> 24 Hours Non-Stop</p>
        <p><strong>🏆 Prizes:</strong> Certificates for Top 3</p>
        <p><strong>🌐 Mode:</strong> Online Global Event</p>
        <hr>
        <p><strong>Latest Updates:</strong></p>
        <ul>
          <li>✅ Registration Portal Active</li>
          <li>🔧 Challenge Categories Finalized</li>
          <li>👥 Expert Mentors Confirmed</li>
          <li>🎯 Practice Challenges Available</li>
        </ul>
        <p><strong>🔗 Website:</strong> <a href="https://cyber-wolf-free-training.web.app/" target="_blank">cyber-wolf-free-training.web.app</a></p>
      </div>
    </div>
  `;

  document.body.appendChild(popup);

  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (popup.parentElement) {
      popup.remove();
    }
  }, 10000);
}

// Power Button functionality
function initializePowerButton() {
  const powerButton = document.getElementById('powerButton');

  if (powerButton) {
    powerButton.addEventListener('click', turnOnTV);
    powerButton.addEventListener('touchstart', turnOnTV);
  }
}

function turnOnTV() {
  const tvScreen = document.getElementById('tvScreen');
  const tvContainer = document.getElementById('tvContainer');
  const tvOffScreen = document.getElementById('tvOffScreen');
  const powerButton = document.getElementById('powerButton');

  // Disable power button
  powerButton.style.pointerEvents = 'none';
  powerButton.style.opacity = '0.5';

  // Add turn on animation
  tvContainer.classList.add('tv-turning-on');

  // Play TV turn on sound effect (if you want to add one)
  console.log('TV turning on...');

  setTimeout(() => {
    // Remove off state classes
    tvScreen.classList.remove('tv-off');
    tvContainer.classList.remove('tv-off');

    // Hide the off screen
    tvOffScreen.classList.add('hidden');

    // Start all TV functionality
    startTVFunctionality();

    // Remove turn on animation class
    setTimeout(() => {
      tvContainer.classList.remove('tv-turning-on');
    }, 1500);

    console.log('Wolf CTF TV is now ON! 🐺📺');
  }, 800);
}

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', initializeCTFDisplay);