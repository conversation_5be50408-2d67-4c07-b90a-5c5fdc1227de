body {
  background: black;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
}
.tv-screen {
  position: absolute;
  width: 90vw;
  height: 50vw;
  max-width: 100%;
  max-height: 100vh;
  background-image: url(https://alexandrevacassin.fr/codepen/old-tv/base.webp);
  z-index: 10;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}
.tv-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60vw;
  height: 45vw;
  max-width: 100%;
  max-height: 70vh;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
video, iframe {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  filter: contrast(1.2) brightness(1.1);
}
.glitch {
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://upload.wikimedia.org/wikipedia/commons/0/02/Television_static.gif");
  mix-blend-mode: multiply;
  opacity: 0.3;
  animation: glitchMove 0.2s infinite linear;
  z-index: 2;
}
.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0px,
    rgba(0, 0, 0, 0.2) 1px,
    rgba(0, 0, 0, 0) 2px
  );
  pointer-events: none;
  z-index: 2;
}
@keyframes glitchMove {
  0% { transform: translateX(0); }
  33% { transform: translateX(-5px); }
  66% { transform: translateX(5px); }
  100% { transform: translateX(0); }
}
canvas {
  mix-blend-mode: screen;
  position: absolute;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.snow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://upload.wikimedia.org/wikipedia/commons/0/02/Television_static.gif");
  background-size: cover;
  opacity: 0;
  z-index: 3;
  pointer-events: none;
  transition: opacity 0.5s ease-in-out;
}

/* CTF Message Scrolling Animation */
.ctf-message {
  position: absolute;
  bottom: -100%;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: auto;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 1.1em;
  line-height: 1.5;
  text-align: center;
  padding: 15px;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid #00ff00;
  border-radius: 5px;
  box-shadow: 0 0 15px #00ff00, inset 0 0 10px rgba(0, 255, 0, 0.1);
  z-index: 5;
  white-space: pre-line;
}

.ctf-message.animate {
  animation: scrollUp 15s linear infinite;
}

@keyframes scrollUp {
  0% {
    bottom: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    bottom: 100%;
    opacity: 0;
  }
}