* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: black;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  font-family: 'Courier New', monospace;
}
.tv-screen {
  position: absolute;
  width: 85vw;
  height: 48vw;
  max-width: 900px;
  max-height: 600px;
  background-image: url(https://alexandrevacassin.fr/codepen/old-tv/base.webp);
  z-index: 10;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}
.tv-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 58vw;
  height: 32vw;
  max-width: 650px;
  max-height: 400px;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
video, iframe {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  filter: contrast(1.2) brightness(1.1);
}

#ctfvideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

/* Video controls styling */
#ctfvideo::-webkit-media-controls {
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.5);
}

#ctfvideo::-webkit-media-controls-panel {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid #00ff00;
}

/* Play button for user interaction */
.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 255, 0, 0.9);
  color: black;
  padding: 15px 25px;
  border: 2px solid #00ff00;
  border-radius: 10px;
  font-family: 'Courier New', monospace;
  font-size: 1.2em;
  font-weight: bold;
  cursor: pointer;
  z-index: 15;
  box-shadow: 0 0 20px #00ff00;
  transition: all 0.3s ease;
  user-select: none;
}

.play-button:hover {
  background: #00ff00;
  box-shadow: 0 0 30px #00ff00;
  transform: translate(-50%, -50%) scale(1.05);
}

.play-button:active {
  transform: translate(-50%, -50%) scale(0.95);
}
.glitch {
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://upload.wikimedia.org/wikipedia/commons/0/02/Television_static.gif");
  mix-blend-mode: multiply;
  opacity: 0.3;
  animation: glitchMove 0.2s infinite linear;
  z-index: 2;
}
.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0px,
    rgba(0, 0, 0, 0.2) 1px,
    rgba(0, 0, 0, 0) 2px
  );
  pointer-events: none;
  z-index: 2;
}
@keyframes glitchMove {
  0% { transform: translateX(0); }
  33% { transform: translateX(-5px); }
  66% { transform: translateX(5px); }
  100% { transform: translateX(0); }
}
canvas {
  mix-blend-mode: screen;
  position: absolute;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.snow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://upload.wikimedia.org/wikipedia/commons/0/02/Television_static.gif");
  background-size: cover;
  opacity: 0;
  z-index: 3;
  pointer-events: none;
  transition: opacity 0.5s ease-in-out;
}

/* CTF Message Scrolling Animation */
.ctf-message {
  position: absolute;
  bottom: -100%;
  left: 50%;
  transform: translateX(-50%);
  width: 95%;
  height: auto;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: clamp(0.7rem, 2vw, 1rem);
  line-height: 1.4;
  text-align: center;
  padding: clamp(8px, 2vw, 15px);
  background: rgba(0, 0, 0, 0.371);
  border: 2px solid #00ff00;
  border-radius: 5px;
  box-shadow: 0 0 15px #00ff00, inset 0 0 10px rgba(0, 255, 0, 0.1);
  z-index: 5;
  white-space: pre-line;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.ctf-message.animate {
  animation: scrollUp 20s linear infinite;
  z-index: 10;
}

@keyframes scrollUp {
  0% {
    bottom: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    bottom: 100%;
    opacity: 0;
  }
}

/* Responsive Design for Different Devices */

/* Tablet Portrait */
@media screen and (max-width: 768px) and (orientation: portrait) {
  .tv-screen {
    width: 95vw;
    height: 60vw;
    max-width: none;
    max-height: 80vh;
  }

  .tv-container {
    width: 75vw;
    height: 42vw;
    max-width: none;
    max-height: 60vh;
  }

  .ctf-message {
    font-size: clamp(0.8rem, 3vw, 1.2rem);
    padding: clamp(10px, 3vw, 20px);
    width: 92%;
  }
}

/* Mobile Portrait */
@media screen and (max-width: 480px) and (orientation: portrait) {
  .tv-screen {
    width: 98vw;
    height: 70vw;
    max-width: none;
    max-height: 85vh;
  }

  .tv-container {
    width: 85vw;
    height: 50vw;
    max-width: none;
    max-height: 65vh;
  }

  .ctf-message {
    font-size: clamp(0.9rem, 4vw, 1.4rem);
    padding: clamp(12px, 4vw, 25px);
    width: 90%;
    line-height: 1.3;
  }
}

/* Mobile Landscape */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .tv-screen {
    width: 85vw;
    height: 75vh;
    max-width: none;
    max-height: none;
  }

  .tv-container {
    width: 70vw;
    height: 55vh;
    max-width: none;
    max-height: none;
  }

  .ctf-message {
    font-size: clamp(0.7rem, 2.5vw, 1rem);
    padding: clamp(8px, 2vw, 15px);
    width: 95%;
  }
}

/* Large Screens */
@media screen and (min-width: 1200px) {
  .tv-screen {
    width: 70vw;
    height: 40vw;
    max-width: 1000px;
    max-height: 650px;
  }

  .tv-container {
    width: 50vw;
    height: 28vw;
    max-width: 750px;
    max-height: 450px;
  }

  .ctf-message {
    font-size: 1.1rem;
    padding: 18px;
  }
}