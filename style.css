* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: black;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  font-family: 'Courier New', monospace;
}
.tv-screen {
  position: absolute;
  width: 85vw;
  height: 48vw;
  max-width: 900px;
  max-height: 600px;
  background-image: url(https://alexandrevacassin.fr/codepen/old-tv/base.webp);
  z-index: 10;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  transition: all 0.5s ease;
}

.tv-screen.tv-off {
  filter: brightness(0.3) contrast(0.5);
}
.tv-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 58vw;
  height: 32vw;
  max-width: 650px;
  max-height: 400px;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  transition: all 0.5s ease;
}

.tv-container.tv-off {
  filter: brightness(0.2);
}

.tv-container.tv-off canvas,
.tv-container.tv-off video,
.tv-container.tv-off .glitch,
.tv-container.tv-off .scan-lines,
.tv-container.tv-off .snow-effect,
.tv-container.tv-off .ctf-message,
.tv-container.tv-off .ctf-news-icon {
  opacity: 0;
  pointer-events: none;
}
video, iframe {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  filter: contrast(1.2) brightness(1.1);
}

#ctfvideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

/* Video controls styling */
#ctfvideo::-webkit-media-controls {
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.5);
}

#ctfvideo::-webkit-media-controls-panel {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid #00ff00;
}

/* CTF News Touch Icon */
.ctf-news-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #00ff00, #00cc00);
  border: 3px solid #00ff00;
  border-radius: 15px;
  cursor: pointer;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0 20px #00ff00,
    0 0 40px rgba(0, 255, 0, 0.3),
    inset 0 0 10px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
  user-select: none;
}

.ctf-news-icon:hover {
  transform: scale(1.1);
  box-shadow:
    0 0 30px #00ff00,
    0 0 60px rgba(0, 255, 0, 0.5),
    inset 0 0 15px rgba(255, 255, 255, 0.329);
  animation: none;
}

.ctf-news-icon:active {
  transform: scale(0.95);
  box-shadow:
    0 0 15px #00ff00,
    inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.icon-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #000;
  font-family: 'Courier New', monospace;
}

.icon-symbol {
  font-size: 1.5em;
  margin-bottom: 2px;
}

.icon-text {
  font-size: 0.9em;
  font-weight: bold;
  margin-bottom: 2px;
}

.touch-indicator {
  font-size: 0.7em;
  font-weight: normal;
  opacity: 0.8;
  animation: blink 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow:
      0 0 20px #00ff00,
      0 0 40px rgba(0, 255, 0, 0.3),
      inset 0 0 10px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px #00ff00,
      0 0 60px rgba(0, 255, 0, 0.5),
      inset 0 0 15px rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow:
      0 0 20px #00ff00,
      0 0 40px rgba(0, 255, 0, 0.3),
      inset 0 0 10px rgba(255, 255, 255, 0.1);
  }
}

@keyframes blink {
  0%, 50% { opacity: 0.8; }
  51%, 100% { opacity: 0.3; }
}

/* TV Off Screen and Power Button */
.tv-off-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, #000000, #000);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 25;
  transition: all 0.8s ease;
}

.tv-off-screen.hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.8);
}

.power-button {
  background: linear-gradient(135deg, #ff0000, #cc0000);
  border: 4px solid #ff0000;
  border-radius: 20px;
  width: 200px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 0 30px #ff0000,
    0 0 60px rgba(255, 0, 0, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  animation: powerPulse 2s infinite;
  user-select: none;
}

.power-button:hover {
  transform: scale(1.1);
  box-shadow:
    0 0 40px #ff0000,
    0 0 80px rgba(255, 0, 0, 0.5),
    inset 0 0 30px rgba(255, 255, 255, 0.2);
  animation: none;
}

.power-button:active {
  transform: scale(0.95);
  box-shadow:
    0 0 20px #ff0000,
    inset 0 0 40px rgba(0, 0, 0, 0.3);
}

.button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: white;
  font-family: 'Courier New', monospace;
}

.power-icon {
  font-size: 2.5em;
  margin-bottom: 5px;
  animation: flash 1.5s infinite;
}

.button-text {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 3px;
  letter-spacing: 2px;
}

.sub-text {
  font-size: 0.9em;
  opacity: 0.8;
  font-style: italic;
}

@keyframes powerPulse {
  0% {
    box-shadow:
      0 0 30px #ff0000,
      0 0 60px rgba(255, 0, 0, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 0 40px #ff0000,
      0 0 80px rgba(255, 0, 0, 0.5),
      inset 0 0 30px rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow:
      0 0 30px #ff0000,
      0 0 60px rgba(255, 0, 0, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
  }
}

@keyframes flash {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* TV Turn On Animation */
.tv-turning-on {
  animation: tvTurnOn 1.5s ease-out;
}

@keyframes tvTurnOn {
  0% {
    filter: brightness(0.2);
    transform: translate(-50%, -50%) scaleY(0.01);
  }
  20% {
    transform: translate(-50%, -50%) scaleY(0.1);
    filter: brightness(0.5);
  }
  40% {
    transform: translate(-50%, -50%) scaleY(0.5);
    filter: brightness(0.8);
  }
  60% {
    transform: translate(-50%, -50%) scaleY(0.8);
    filter: brightness(1.2);
  }
  80% {
    transform: translate(-50%, -50%) scaleY(0.95);
    filter: brightness(1.1);
  }
  100% {
    transform: translate(-50%, -50%) scaleY(1);
    filter: brightness(1);
  }
}

/* CTF News Popup */
.ctf-news-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.news-content {
  background: linear-gradient(135deg, #001100, #003300);
  border: 3px solid #00ff00;
  border-radius: 15px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow:
    0 0 30px #00ff00,
    0 0 60px rgba(0, 255, 0, 0.3),
    inset 0 0 20px rgba(0, 255, 0, 0.1);
  animation: slideIn 0.4s ease;
}

.news-header {
  background: #00ff00;
  color: #000;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

.news-header h2 {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 1.2em;
  animation: pulse 1s infinite;
}

.close-btn {
  background: #ff0000;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 1.2em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff3333;
  transform: scale(1.1);
}

.news-body {
  padding: 20px;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  line-height: 1.5;
}

.news-body p {
  margin: 10px 0;
}

.news-body strong {
  color: #00ffff;
}

.news-body ul {
  margin: 10px 0;
  padding-left: 20px;
}

.news-body li {
  margin: 5px 0;
}

.news-body hr {
  border: none;
  border-top: 1px solid #00ff00;
  margin: 15px 0;
}

.news-body a {
  color: #00ffff;
  text-decoration: underline;
}

.news-body a:hover {
  color: #ffffff;
  text-shadow: 0 0 5px #00ffff;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    transform: scale(0.8) translateY(-50px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}


.glitch {
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://upload.wikimedia.org/wikipedia/commons/0/02/Television_static.gif");
  mix-blend-mode: multiply;
  opacity: 0.3;
  animation: glitchMove 0.2s infinite linear;
  z-index: 2;
}
.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0px,
    rgba(0, 0, 0, 0.2) 1px,
    rgba(0, 0, 0, 0) 2px
  );
  pointer-events: none;
  z-index: 2;
}
@keyframes glitchMove {
  0% { transform: translateX(0); }
  33% { transform: translateX(-5px); }
  66% { transform: translateX(5px); }
  100% { transform: translateX(0); }
}
canvas {
  mix-blend-mode: screen;
  position: absolute;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.snow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://upload.wikimedia.org/wikipedia/commons/0/02/Television_static.gif");
  background-size: cover;
  opacity: 0;
  z-index: 3;
  pointer-events: none;
  transition: opacity 0.5s ease-in-out;
}

/* CTF Message Scrolling Animation */
.ctf-message {
  position: absolute;
  bottom: -100%;
  left: 50%;
  transform: translateX(-50%);
  width: 95%;
  height: auto;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: clamp(0.7rem, 2vw, 1rem);
  line-height: 1.4;
  text-align: center;
  padding: clamp(8px, 2vw, 15px);
  background: rgba(0, 0, 0, 0.371);
  border: 2px solid #00ff0000;
  border-radius: 5px;
  box-shadow: 0 0 15px #00ff0000, inset 0 0 10px rgba(0, 255, 0, 0.1);
  z-index: 5;
  white-space: pre-line;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.ctf-message.animate {
  animation: scrollUp 20s linear infinite;
  z-index: 10;
}

@keyframes scrollUp {
  0% {
    bottom: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    bottom: 100%;
    opacity: 0;
  }
}

/* Responsive Design for Different Devices */

/* Tablet Portrait */
@media screen and (max-width: 768px) and (orientation: portrait) {
  .tv-screen {
    width: 95vw;
    height: 60vw;
    max-width: none;
    max-height: 80vh;
  }

  .tv-container {
    width: 75vw;
    height: 42vw;
    max-width: none;
    max-height: 60vh;
  }

  .ctf-message {
    font-size: clamp(0.8rem, 3vw, 1.2rem);
    padding: clamp(10px, 3vw, 20px);
    width: 92%;
  }
}

/* Mobile Portrait */
@media screen and (max-width: 480px) and (orientation: portrait) {
  .tv-screen {
    width: 98vw;
    height: 70vw;
    max-width: none;
    max-height: 85vh;
  }

  .tv-container {
    width: 85vw;
    height: 50vw;
    max-width: none;
    max-height: 65vh;
  }

  .ctf-message {
    font-size: clamp(0.9rem, 4vw, 1.4rem);
    padding: clamp(12px, 4vw, 25px);
    width: 90%;
    line-height: 1.3;
  }

  .ctf-news-icon {
    top: 10px;
    right: 10px;
    width: 100px;
    height: 70px;
  }

  .icon-symbol {
    font-size: 1.3em !important;
  }

  .icon-text {
    font-size: 0.8em !important;
  }

  .touch-indicator {
    font-size: 0.6em !important;
  }

  .news-content {
    max-width: 90vw;
    margin: 10px;
  }

  .news-header h2 {
    font-size: 1em;
  }

  .news-body {
    padding: 15px;
    font-size: 0.9em;
  }

  .power-button {
    width: 160px;
    height: 100px;
  }

  .power-icon {
    font-size: 2em !important;
  }

  .button-text {
    font-size: 1em !important;
  }

  .sub-text {
    font-size: 0.8em !important;
  }
}

/* Mobile Landscape */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .tv-screen {
    width: 85vw;
    height: 75vh;
    max-width: none;
    max-height: none;
  }

  .tv-container {
    width: 70vw;
    height: 55vh;
    max-width: none;
    max-height: none;
  }

  .ctf-message {
    font-size: clamp(0.7rem, 2.5vw, 1rem);
    padding: clamp(8px, 2vw, 15px);
    width: 95%;
  }
}

/* Large Screens */
@media screen and (min-width: 1200px) {
  .tv-screen {
    width: 70vw;
    height: 40vw;
    max-width: 1000px;
    max-height: 650px;
  }

  .tv-container {
    width: 50vw;
    height: 28vw;
    max-width: 750px;
    max-height: 450px;
  }

  .ctf-message {
    font-size: 1.1rem;
    padding: 18px;
  }
}